import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/screens/schedule_module/ui/month_screens/schedule_month_time_detail_screen.dart';

import '../../../../common/custom_widgets/spacebox.dart';
import '../../../../utils/app_navigation/appnavigation.dart';
import '../../../../utils/appsize.dart';
import '../../../../utils/colors/app_colors.dart';
import '../../../../utils/shift_time_formatter.dart';
import '../../bloc/schedule_cubit.dart';

class MonthDetailWidget extends StatelessWidget {
  final int index;
  const MonthDetailWidget({Key? key, required this.index}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final scheduleBloc = BlocProvider.of<ScheduleCubit>(context);

    return GestureDetector(
      onTap: () {
        AppNavigation.nextScreen(
          context,
          ScheduleMonthTimeDetailScreen(
            scheduleMonthData: scheduleBloc.scheduleMonthList1[index],
          ),
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: AppSize.w10, vertical: AppSize.h10),
        decoration: BoxDecoration(
          color: context.themeColors.homeContainerColor,
          border: Border(
              left: BorderSide(
                  color:

                      /// Color should be displayed based on the shift type like rooster, ruilen, open diensten
                      scheduleBloc.scheduleMonthList1[index].swap != null &&
                              (scheduleBloc.scheduleMonthList1[index].swap
                                          ?.state ==
                                      'Aangevraagd' ||
                                  scheduleBloc.scheduleMonthList1[index].swap
                                          ?.state ==
                                      'Geaccepteerd' ||
                                  scheduleBloc.scheduleMonthList1[index].swap
                                          ?.state ==
                                      'Uitgezet')
                          ? AppColors.ruilenColor
                          : scheduleBloc.scheduleMonthList1[index].openService ==
                                      null &&
                                  (scheduleBloc.scheduleMonthList1[index].swap ==
                                          null ||
                                      scheduleBloc.scheduleMonthList1[index].swap
                                              ?.state ==
                                          null)
                              ? AppColors.roosterColor
                              : scheduleBloc.scheduleMonthList1[index]
                                          .openService !=
                                      null
                                  ? AppColors.openDienstenColor
                                  : AppColors.transparent,
                  width: AppSize.sp6)),
        ),
        // margin: EdgeInsets.only(top: AppSize.h10, bottom: AppSize.h10),
        child: Column(
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ShiftTimeFormatter.formatShiftTimeForList(
                        scheduleBloc.scheduleMonthList1[index].timeFrom,
                        scheduleBloc.scheduleMonthList1[index].timeUntil,
                      ),
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.themeColors.textColor,
                        fontSize: AppSize.sp14,
                      ),
                    ),
                    SpaceV(AppSize.h2),
                    Text(
                      scheduleBloc.scheduleMonthList1[index].department
                          .toString(),
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.themeColors.textColor,
                        fontSize: AppSize.sp12,
                      ),
                    ),
                  ],
                ),
                Spacer(),
                (scheduleBloc.scheduleMonthList1[index].swap?.state != null)
                    ? Icon(
                        Ionicons.swap_horizontal_outline,
                        size: AppSize.sp20,
                        color: AppColors.primaryColor,
                      )
                    : (scheduleBloc.scheduleMonthList1[index].openService !=
                            null)
                        ? Icon(
                            Ionicons.create_outline,
                            size: AppSize.sp20,
                            color: AppColors.primaryColor,
                          )
                        : Container(),
                Padding(
                  padding:
                      EdgeInsets.only(right: AppSize.w10, left: AppSize.w4),
                  child: Icon(
                    Icons.arrow_forward_ios_sharp,
                    size: AppSize.sp14,
                    color: context.themeColors.iconColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
